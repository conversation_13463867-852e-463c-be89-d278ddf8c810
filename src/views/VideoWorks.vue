<template>
  <div class="video-works-component">
    <!-- 页面标题 -->
    <!-- <div class="page-header">
      <h1 class="page-title">视频作品精选</h1>
    </div> -->

    <!-- 使用封装的视频作品网格组件 -->
    <VideoWorksGrid
      @video-click="viewVideo"
      @share-video="shareVideo"
      @load-more="handleLoadMore"
    />
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import VideoWorksGrid from '@/components/VideoWorksGrid.vue'

const router = useRouter()

// 查看视频
const viewVideo = (video) => {
  // 如果有分享码，跳转到视频分享页面
}

// 分享视频
const shareVideo = (video) => {
}

// 处理加载更多事件
const handleLoadMore = (data) => {
  console.log('加载更多:', data)
}
</script>

<style scoped>
.video-works-component {
  padding: 0 10px;
  width: 100%;
}

/* 页面标题样式 */
.page-header {
  text-align: center;
  padding: 10px 0;
  text-align: left;
}

.page-title {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  background: linear-gradient(135deg, #6365f1b0, #8a5cf6b7);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: title-glow 3s ease-in-out infinite alternate;
}

body.dark .page-title {
  color: #e5e7eb;
}

@keyframes title-glow {
  0% {
    filter: brightness(1);
  }
  100% {
    filter: brightness(1.1);
  }
}
</style>
