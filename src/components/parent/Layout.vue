<template>
  <div class="app-layout">
    <SideNav @collapse-change="handleCollapseChange" ref="sideNavRef" v-if="isRouteName()"/>
    <div class="content-container" :class="{ 'with-sidebar': !isSidebarCollapsed }">
      <Header :transparent-strong="true" class="header" :backButton="!isRouteName()" v-if="isHeaderRouteName()"/>
      <main class="main-content" :class="{ 'height-50': isHeaderRouteName() }">
        <slot></slot>
      </main>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted, onUnmounted} from 'vue';
import Header from './Header.vue';
import SideNav from './SideNav.vue';
import {useRoute} from 'vue-router';

const route = useRoute();
const sideNavRef = ref(null);
const isSidebarCollapsed = ref(false);

// 处理折叠状态变化
const handleCollapseChange = (collapsed) => {
  isSidebarCollapsed.value = collapsed;
};

const isRouteName = () => {
  return route.name === 'FeaturedWorks' ||
      route.name === 'InputSection' ||
      route.name === 'NeoAssets' ||
      route.name === 'Home' ||
      route.name === 'Stories' ||
      route.name === 'Profile' ||
      route.name === 'CanvasEditor' ||
      route.name === 'ImageStudio' ||
      route.name === 'VideoStudio' ||
      route.name === 'AudioStudio' ||
      route.name === 'VideoWorks'
}

const isHeaderRouteName = () => {
  return route.name != 'Login' && route.name != 'VideoEditor' && route.name != 'Share' && route.name != 'VideoShare' 
}

// 监听全局折叠状态变化事件（用于处理可能的外部折叠触发）
const handleGlobalCollapseChange = (event) => {
  if (event.detail && event.detail.hasOwnProperty('collapsed')) {
    isSidebarCollapsed.value = event.detail.collapsed;
  }
};

// 检查屏幕宽度并自动折叠侧边栏
const checkScreenWidth = () => {
  const isMobile = window.innerWidth <= 768;

  // 在移动端视图下，自动折叠侧边栏
  if (isMobile && !isSidebarCollapsed.value) {
    isSidebarCollapsed.value = true;

    // 如果有侧边栏引用，同步更新其状态
    if (sideNavRef.value && typeof sideNavRef.value.toggleCollapse === 'function') {
      // 调用SideNav的折叠方法，确保状态同步
      sideNavRef.value.toggleCollapse();
    } else {
      // 如果无法直接调用方法，则保存到本地存储
      localStorage.setItem('sideNavCollapsed', 'true');
    }
  } else if (!isMobile && isSidebarCollapsed.value) {
    isSidebarCollapsed.value = false;
    if (sideNavRef.value && typeof sideNavRef.value.toggleCollapse === 'function') {
      sideNavRef.value.toggleCollapse();
    } else {
      localStorage.setItem('sideNavCollapsed', 'false');
    }
  }
};

onMounted(() => {
  // 读取侧边栏状态
  const savedCollapsedState = localStorage.getItem('sideNavCollapsed');
  if (savedCollapsedState !== null) {
    isSidebarCollapsed.value = savedCollapsedState === 'true';
  }

  // 监听全局折叠状态变化事件
  window.addEventListener('sidenav-collapse-change', handleGlobalCollapseChange);

  // 添加窗口大小变化事件监听
  window.addEventListener('resize', checkScreenWidth);

  // 初始化时检查一次屏幕宽度
  checkScreenWidth();

  // 如果能直接访问SideNav组件的状态，则使用它的状态
  setTimeout(() => {
    if (sideNavRef.value && sideNavRef.value.isCollapsed !== undefined) {
      isSidebarCollapsed.value = sideNavRef.value.isCollapsed;
      // 再次检查屏幕宽度，确保在小屏幕下始终为折叠状态
      checkScreenWidth();
    }
  }, 0);
});

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('sidenav-collapse-change', handleGlobalCollapseChange);
  window.removeEventListener('resize', checkScreenWidth);
});
</script>

<style scoped>
.app-layout {
  display: flex;
  flex-direction: row;
  width: 100%;
  min-height: 100vh;
  background-color: #f5f7fa;
}

body.dark .app-layout {
  background-color: var(--bg-primary);
}

.header {
  /* position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10; */
}

.content-container {
  flex: 1;
  /* 折叠状态下的宽度 */
  transition: margin-left 0.3s ease;
  /* margin-left: 60px;
  width: calc(100% - 60px); */
}

.content-container.with-sidebar {
  /* margin-left: 200px;
  width: calc(100% - 200px); */
}

.main-content {
  /* padding: 20px; */
  height: 100vh;
  overflow-y: auto;
}

.main-content.height-50 {
  height: calc(100vh - 50px);
}

/* @media screen and (max-width: 768px) {
  .content-container {
    margin-left: 0;
    width: 100%;
  }
  .content-container.with-sidebar {
    margin-left: 0;
    width: 100%;
  }
} */
</style>