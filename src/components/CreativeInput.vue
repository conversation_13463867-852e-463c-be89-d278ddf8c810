<template>
  <div class="creative-input">
    <div class="section-header">
      <h2 class="title-with-icon">
        <el-icon class="title-icon">
          <MagicStick />
        </el-icon>
        <span class="gradient-text">AI 故事工坊</span>
      </h2>
      <!-- <div class="input-counter">{{ modelValue.length }}/500</div> -->
    </div>

    <div class="input-wrapper">

      <!-- 隐藏的文件输入 -->
      <input ref="fileInputRef" type="file" style="display: none" @change="handleMultipleFilesChange" multiple
        accept="image/*" />

      <div class="bt_content_add">

        <div class="multiple-file-container" v-if="multipleFiles.length > 0">
          <div class="multiple-file-item" v-for="file in multipleFiles" :key="file.id" :class="{
            'is-uploading': file.status === 'uploading',
            'is-success': file.status === 'success',
            'is-error': file.status === 'error'
          }">
            <div class="multiple-file-item-preview">
              <!-- 图片预览 -->
              <img v-if="file.type === FILE_TYPES.IMAGE" :src="file.url" class="file-preview-image" />

              <!-- 视频预览 -->
              <video v-else-if="file.type === FILE_TYPES.VIDEO" :src="file.url" class="file-preview-video"
                controls></video>

              <!-- 其他文件类型 -->
              <div v-else class="file-preview-icon">
                <el-icon>
                  <component :is="getFileIcon(file.type)" />
                </el-icon>
              </div>

              <!-- 上传状态覆盖层 -->
              <div class="file-status-overlay" v-if="file.status !== 'success'">
                <el-progress v-if="file.status === 'uploading'" type="circle" :percentage="uploadProgress[file.id] || 0"
                  :width="30" :stroke-width="4" status="success" />
                <el-icon v-else-if="file.status === 'error'" class="error-icon">
                  <CircleCloseFilled />
                </el-icon>
              </div>
              <div class="multiple-file-item-actions">
                <el-button type="danger" size="small" circle @click="removeFile(file.id)"
                  :disabled="multipleFilesUploading">
                  <el-icon>
                    <Close />
                  </el-icon>
                </el-button>
              </div>
              <div class="multiple-file-item-info">
                <!-- <div class="multiple-file-item-name">{{ file.file.name }}</div> -->
                <div class="multiple-file-item-size">{{ (file.file.size / 1024) >= 1024 ?
                  ((file.file.size / 1024 / 1024).toFixed(1) + 'M') :
                  (Math.round(file.file.size / 1024) + 'K') }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 上传按钮 :disabled="!isFormValid" -->
        <div @click="triggerFileUpload" class="upload-button custom-upload-btn">
          <div class="upload-button-icon">
            <el-icon>
              <Plus />
            </el-icon>
            <span class="upload-button-text">参考</span>
          </div>
        </div>

        <!-- show-word-limit -->
        <el-input :model-value="modelValue" @update:model-value="updateValue" type="textarea"
          :autosize="{ minRows: 2, maxRows: 12 }" maxlength="1000" resize="none" :placeholder="placeholder"
          class="typewriter-input">
        </el-input>

      </div>

      <div class="bt_content">
        <!-- 显示 multipleFiles -->
        <!-- <div class="multiple-files-display" v-if="multipleFiles.length > 0">
          <span class="files-count">{{ multipleFiles.length }}个文件</span>
        </div> -->

        <!-- 上传按钮 :disabled="!isFormValid" -->
        <!-- <el-button type="default" @click="triggerFileUpload"
          class="upload-button custom-upload-btn" round>
          <el-icon>
            <Plus />
          </el-icon>
        </el-button> -->

        <!-- 添加已选设置信息区域 -->
        <div class="selected-settings" v-if="hasSelectedSettings">

          <!-- 创作类型选择器 -->
          <CreationTypeSelector
            currentType="story"
            @type-change="handleTypeChange"
          />

          <!-- 音色选择 DropdownPanel -->
          <DropdownPanel v-model="showVoiceSelector" position="bottom" align="center" closeOnClickOutside :width="860"
            :zIndex="1200" :maxHeight="360" dropdown-id="voice-selector">
            <template #trigger>
              <div class="setting-item" :class="{ 'setting-changed': voiceChanged }">
                <el-icon>
                  <Microphone />
                </el-icon>
                <span class="setting-text">{{ getVoiceName }}</span>
              </div>
            </template>
            <div class="dropdown-content-wrapper">
              <VoiceSelector :voices="voices" :modelValue="selectedVoice" :is-loading="isLoadingVoices"
                @update:modelValue="handleVoiceChange" @refresh-voices="handleRefreshVoices" />
            </div>
          </DropdownPanel>

          <!-- 风格选择 DropdownPanel -->
          <DropdownPanel v-model="showStyleSelector" position="bottom" align="center" closeOnClickOutside :width="760"
            :zIndex="1200" :maxHeight="500" dropdown-id="style-selector">
            <template #trigger>
              <div class="setting-item" :class="{ 'setting-changed': styleChanged }">
                <el-icon>
                  <PictureFilled />
                </el-icon>
                <span class="setting-text">{{ getStyleName }}</span>
              </div>
            </template>
            <div class="dropdown-content-wrapper">
              <StyleSelector :styles="styles" :modelValue="selectedStyle" :isLoading="isLoadingStyles"
                @update:modelValue="handleStyleChange" />
            </div>
          </DropdownPanel>

          <!-- 模型选择 DropdownPanel -->
          <DropdownPanel v-model="showModelSelector" position="bottom" align="center" closeOnClickOutside :width="320"
            :zIndex="1200" :maxHeight="400" dropdown-id="model-selector" v-if="false">
            <template #trigger>
              <div class="setting-item" :class="{ 'setting-changed': modelChanged }">
                <el-icon>
                  <Operation />
                </el-icon>
                <span class="setting-text">{{ getModelName }}</span>
              </div>
            </template>
            <div class="dropdown-content-wrapper">
              <ImageModelSelector :modelValue="selectedModel" :models="imageModels"
                @update:modelValue="handleModelChange" />
            </div>
          </DropdownPanel>

          <!-- 比例选择 DropdownPanel -->
          <DropdownPanel v-model="showRatioSelector" position="bottom" align="center" closeOnClickOutside :width="500"
            :zIndex="1200" :maxHeight="360" dropdown-id="ratio-selector">
            <template #trigger>
              <div class="setting-item" :class="{ 'setting-changed': ratioChanged }">
                <el-icon>
                  <Crop />
                </el-icon>
                <span class="setting-text">{{ outputRatio }}</span>
              </div>
            </template>
            <div class="dropdown-content-wrapper">
              <RatioSelector :modelValue="outputRatio" :ratioOptions="aspectRatioOptions.map(r => r.value)"
                @update:modelValue="handleRatioChange" />
            </div>
          </DropdownPanel>
        </div>

        <el-button type="primary" :disabled="!isFormValid" @click="handleStartCreation" class="send-button"
          :loading="props.isCreating" round>
          <el-icon class="send-icon">
            <Position />
          </el-icon>
          <!-- <span>发送</span> -->
        </el-button>

      </div>
    </div>

    <!-- 来源选择区域 -->
    <div class="source-selection" v-if="false">
      <div class="source-content">
        <!-- 摘要选择内容 -->
        <div v-if="activeSourceType === SOURCE_TYPES.SUMMARY" class="source-panel summary-panel">
          <!-- 摘要中选择 -->

          <div class="input-suggestions">
            <!-- <div class="suggestion-title">创意提示：</div> -->
            <div v-if="isLoadingSuggestions" class="suggestions-loading">
              <el-skeleton :rows="4" animated />
            </div>
            <div v-else-if="suggestions.length === 0" class="suggestions-empty">
              暂无创意提示
            </div>
            <div v-else class="suggestions-categories">
              <!-- <div class="category-title">创意提示：</div> -->
              <div class="suggestion-tags">
                <div v-for="(group, groupIndex) in groupedSuggestions" :key="groupIndex" class="suggestion-category">
                  <div v-for="(suggestion, index) in group.items" :key="index" class="suggestion-tag"
                    @click="addSuggestion(suggestion.prompt)" v-if="groupIndex <= 1">
                    {{ suggestion.name }}
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="none">
                      <path fill="currentColor"
                        d="M6.588 2.503a.583.583 0 0 1 .825 0l4.083 4.083a.583.583 0 1 1-.825.825L7.583 4.324v6.758a.583.583 0 1 1-1.166 0V4.324L3.329 7.41a.583.583 0 0 1-.825-.825z">
                      </path>
                    </svg>
                  </div>

                  <!-- <div class="category-title">{{ group.category }}</div>
                <div class="suggestion-tags">
                  <div v-for="(suggestion, index) in group.items" :key="index" class="suggestion-tag"
                    @click="addSuggestion(suggestion.prompt)">
                    {{ suggestion.name }}
                  </div>
                </div> -->
                </div>
              </div>

              <div class="suggestions-header">
                <!-- 换一批 -->
                <el-button class="upload-button custom-upload-btn" type="primary" plain size="small"  style="width:28px ;height: 28px; border-radius: 50%; padding: 0; margin-left: 10px;"
                  @click="refreshSuggestions" :loading="isLoadingSuggestions">
                  <el-icon>
                    <Refresh />
                  </el-icon>
                </el-button>

                <!-- 创作喜好 -->
                <el-button class="upload-button custom-upload-btn" type="primary" plain size="small" style="width:28px ;height: 28px; border-radius: 50%; padding: 0; margin-left: 10px;"
                  @click="openSetting('prefs')" :loading="isLoadingSuggestions">
                  <el-icon>
                    <MagicStick />
                  </el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>

    <!-- <div class="creation-options">
      <div class="option-item">
        <el-checkbox :modelValue="confirmEachStep" @update:modelValue="$emit('update:confirmEachStep', $event)">
          每一步完成后等待确认
        </el-checkbox>
      </div>
      <div class="option-item">
        <el-checkbox :modelValue="highQuality" @update:modelValue="$emit('update:highQuality', $event)">
          生成高质量作品（耗时更长）
        </el-checkbox>
      </div>
    </div> -->

    <!-- 已移除开始创作按钮 -->
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { MagicStick, Document, Upload, Link, PictureFilled, VideoPlay, MoreFilled, CircleCloseFilled, Delete, Plus, Refresh, Position, Microphone, Crop, Operation, Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { uploadToOSS } from '@/api/oss.js'
import DropdownPanel from '@/components/parent/DropdownPanel.vue'
import StyleSelector from '@/components/selector/StyleSelector.vue'
import VoiceSelector from '@/components/selector/VoiceSelector.vue'
import RatioSelector from '@/components/selector/RatioSelector.vue'
import ImageModelSelector from '@/components/selector/ImageModelSelector.vue'
import CreationTypeSelector from '@/components/creation/CreationTypeSelector.vue'

const router = useRouter()

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  selectedStyle: {
    type: Number,
    default: null
  },
  styleName: {
    type: String,
    default: ''
  },
  selectedActors: {
    type: Array,
    default: () => []
  },
  actorNames: {
    type: Array,
    default: () => []
  },
  selectedVoice: {
    type: Number,
    default: null
  },
  voiceName: {
    type: String,
    default: ''
  },
  selectedModel: {
    type: String,
    default: null
  },
  modelName: {
    type: String,
    default: ''
  },
  outputRatio: {
    type: String,
    default: '16:9'
  },
  confirmEachStep: {
    type: Boolean,
    default: true
  },
  highQuality: {
    type: Boolean,
    default: false
  },
  isCreating: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: "请输入你的创意描述"
  },
  suggestions: {
    type: Array,
    default: () => []
  },
  isLoadingSuggestions: {
    type: Boolean,
    default: false
  },
  selectedFile: {
    type: Object,
    default: null
  },
  selectedFileUrl: {
    type: String,
    default: ''
  },
  selectedUrl: {
    type: String,
    default: ''
  },
  conversationId: {
    type: String,
    default: ''
  },
  uploadedFiles: {
    type: Array,
    default: () => []
  },
  // 新增的选择器相关 props
  styles: {
    type: Array,
    default: () => []
  },
  isLoadingStyles: {
    type: Boolean,
    default: false
  },
  voices: {
    type: Array,
    default: () => []
  },
  isLoadingVoices: {
    type: Boolean,
    default: false
  },
  imageModels: {
    type: Array,
    default: () => []
  },
  aspectRatioOptions: {
    type: Array,
    default: () => [
      { label: '1:1', value: '1:1', width: 1024, height: 1024 },
      { label: '16:9', value: '16:9', width: 1920, height: 1080 },
      { label: '9:16', value: '9:16', width: 1080, height: 1920 },
      { label: '4:3', value: '4:3', width: 1024, height: 768 },
      { label: '3:4', value: '3:4', width: 768, height: 1024 }
    ]
  }
})

// 跟踪上一次的设置值，用于检测变化
const prevSelectedStyle = ref(props.selectedStyle)
const prevSelectedVoice = ref(props.selectedVoice)
const prevSelectedModel = ref(props.selectedModel)
const prevOutputRatio = ref(props.outputRatio)

// 设置项动效状态
const styleChanged = ref(false)
const voiceChanged = ref(false)
const modelChanged = ref(false)
const ratioChanged = ref(false)

// DropdownPanel 显示状态
const showStyleSelector = ref(false)
const showVoiceSelector = ref(false)
const showModelSelector = ref(false)
const showRatioSelector = ref(false)

// 选择器变化处理方法
const handleStyleChange = (value) => {
  emit('update:selectedStyle', value)
  styleChanged.value = true
  setTimeout(() => {
    styleChanged.value = false
  }, 1000)
  showStyleSelector.value = false
}

// 处理创作类型变化
const handleTypeChange = (type) => {
  // emit('type-change', type)
}

const handleVoiceChange = (value) => {
  emit('update:selectedVoice', value)
  voiceChanged.value = true
  setTimeout(() => {
    voiceChanged.value = false
  }, 1000)
  showVoiceSelector.value = false
}

const handleModelChange = (value) => {
  emit('update:selectedModel', value)
  modelChanged.value = true
  setTimeout(() => {
    modelChanged.value = false
  }, 1000)
  showModelSelector.value = false
}

const handleRatioChange = (value) => {
  emit('update:outputRatio', value)
  ratioChanged.value = true
  setTimeout(() => {
    ratioChanged.value = false
  }, 1000)
  showRatioSelector.value = false
}

const handleRefreshVoices = () => {
  emit('refresh-voices')
}

// 打字机效果状态
const isTyping = ref(false)

// 打字机效果函数
const typewriterEffect = (text, callback = null) => {
  if (isTyping.value) return; // 如果正在打字，则不执行

  isTyping.value = true;
  let currentText = props.modelValue;

  const fullText = text;
  const totalDuration = 1000; // 总时间固定为2秒
  const totalChars = fullText.length;

  // 处理空文本或极短文本的情况
  if (totalChars <= 0) {
    isTyping.value = false;
    if (callback && typeof callback === 'function') {
      callback();
    }
    return;
  }

  // 先清空文本
  emit('update:modelValue', '');

  // 计算每个字符的时间间隔
  const intervalPerChar = totalDuration / totalChars;

  // 使用 requestAnimationFrame 实现更平滑的动画
  let startTime = null;

  const animate = (timestamp) => {
    if (!startTime) startTime = timestamp;

    // 计算已经过去的时间
    const elapsedTime = timestamp - startTime;

    // 根据已过时间计算应该显示的字符数
    const charsToShow = Math.min(Math.floor(elapsedTime / intervalPerChar), totalChars);

    // 更新文本
    emit('update:modelValue', fullText.substring(0, charsToShow));

    // 如果还没显示完所有字符且未超时，继续动画
    if (charsToShow < totalChars && elapsedTime < totalDuration) {
      requestAnimationFrame(animate);
    } else {
      // 确保显示完整文本
      emit('update:modelValue', fullText);
      isTyping.value = false;
      if (callback && typeof callback === 'function') {
        callback();
      }
    }
  };

  // 开始动画
  requestAnimationFrame(animate);
};

// 监听设置项变化
watch(() => props.selectedStyle, (newVal, oldVal) => {
  if (oldVal !== null && newVal !== oldVal) {
    styleChanged.value = true
    setTimeout(() => {
      styleChanged.value = false
    }, 2000) // 2秒后移除动效
  }
  prevSelectedStyle.value = newVal
}, { immediate: true })

watch(() => props.selectedVoice, (newVal, oldVal) => {
  if (oldVal !== null && newVal !== oldVal) {
    voiceChanged.value = true
    setTimeout(() => {
      voiceChanged.value = false
    }, 2000) // 2秒后移除动效
  }
  prevSelectedVoice.value = newVal
}, { immediate: true })

watch(() => props.selectedModel, (newVal, oldVal) => {
  if (oldVal !== null && newVal !== oldVal) {
    modelChanged.value = true
    setTimeout(() => {
      modelChanged.value = false
    }, 2000) // 2秒后移除动效
  }
  prevSelectedModel.value = newVal
}, { immediate: true })

watch(() => props.outputRatio, (newVal, oldVal) => {
  if (oldVal !== null && newVal !== oldVal) {
    ratioChanged.value = true
    setTimeout(() => {
      ratioChanged.value = false
    }, 2000) // 2秒后移除动效
  }
  prevOutputRatio.value = newVal
}, { immediate: true })

const emit = defineEmits([
  'update:modelValue',
  'clear-style',
  'remove-actor',
  'clear-voice',
  'update:confirmEachStep',
  'update:highQuality',
  'start-creation',
  'update:selectedFile',
  'update:selectedFileUrl',
  'update:selectedUrl',
  'update:uploadedFiles',
  'refresh-suggestions',
  'open-setting',
  'update:selectedStyle',
  'update:selectedVoice',
  'update:selectedModel',
  'update:outputRatio',
  'refresh-voices'
])

const SOURCE_TYPES = {
  SUMMARY: 'summary',
  FILE: 'file',
  URL: 'url'
}

const activeSourceType = ref(SOURCE_TYPES.SUMMARY)

const changeSourceType = (type) => {
  activeSourceType.value = type
}

const fileInputRef = ref(null)
const uploadedFile = ref(null)
const uploadedFileUrl = ref('')
const isUploading = ref(false)

const multipleFiles = ref([])
const multipleFilesUploading = ref(false)
const uploadProgress = ref({})

const FILE_TYPES = {
  IMAGE: 'image',
  VIDEO: 'video',
  OTHER: 'other'
}

const getFileType = (file) => {
  if (!file) return FILE_TYPES.OTHER

  const type = file.type || ''

  if (type.startsWith('image/')) {
    return FILE_TYPES.IMAGE
  } else if (type.startsWith('video/')) {
    return FILE_TYPES.VIDEO
  } else {
    return FILE_TYPES.OTHER
  }
}

const getFileIcon = (fileType) => {
  switch (fileType) {
    case FILE_TYPES.IMAGE:
      return PictureFilled
    case FILE_TYPES.VIDEO:
      return VideoPlay
    default:
      return MoreFilled
  }
}

const handleMultipleFilesChange = (event) => {
  console.log('handleMultipleFilesChange')
  const files = event.target.files
  if (!files || files.length === 0) return

  const invalidFiles = []
  const validFiles = []

  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    const isLt5M = file.size / 1024 / 1024 < 5

    if (isLt5M) {
      validFiles.push({
        file,
        id: `file_${Date.now()}_${i}`,
        url: URL.createObjectURL(file),
        type: getFileType(file),
        status: 'pending',
        progress: 0
      })
    } else {
      invalidFiles.push(file.name)
    }
  }

  if (invalidFiles.length > 0) {
    ElMessage.warning(`${invalidFiles.join(', ')} 超过5MB限制，已跳过`)
  }

  if (validFiles.length > 0) {
    multipleFiles.value = [...multipleFiles.value, ...validFiles]
    console.log('multipleFiles', multipleFiles.value)
    startUploadToOSS()
  }

  event.target.value = ''
}

const startUploadToOSS = async () => {
  console.log('startUploadToOSS')

  const pendingFiles = multipleFiles.value.filter(f => f.status === 'pending')
  if (pendingFiles.length === 0) return

  multipleFilesUploading.value = true

  try {
    pendingFiles.forEach(file => {
      file.status = 'uploading'
      uploadProgress.value[file.id] = 0
    })

    const uploadPromises = pendingFiles.map(async (fileObj) => {
      try {
        // 获取用户ID
        const userInfo = JSON.parse(localStorage.getItem('userInfo'))
        const userId = userInfo.userId
        const result = await uploadToOSS(fileObj.file, userId)

        if (result && result.url) {
          fileObj.status = 'success'
          fileObj.ossUrl = result.url
          fileObj.objectName = result.objectName
          uploadProgress.value[fileObj.id] = 100

          return result
        } else {
          throw new Error('上传失败，未获取到URL')
        }
      } catch (error) {
        fileObj.status = 'error'
        fileObj.error = error.message || '上传失败'
        console.error('文件上传失败:', error)
        return null
      }
    })

    const results = await Promise.all(uploadPromises)
    const successResults = results.filter(r => r !== null)

    const successFiles = multipleFiles.value
      .filter(f => f.status === 'success')
      .map(f => ({
        url: f.ossUrl,
        objectName: f.objectName,
        name: f.file.name,
        type: f.type,
        size: f.file.size
      }))

    emit('update:uploadedFiles', successFiles)

    // if (successResults.length > 0) {
    //   ElMessage.success(`成功上传 ${successResults.length} 个文件`)
    // }
  } catch (error) {
    console.error('批量上传文件失败:', error)
    ElMessage.error('批量上传文件失败，请重试')
  } finally {
    multipleFilesUploading.value = false
  }
}

const removeFile = (fileId) => {
  const index = multipleFiles.value.findIndex(f => f.id === fileId)
  if (index !== -1) {
    if (multipleFiles.value[index].url) {
      URL.revokeObjectURL(multipleFiles.value[index].url)
    }

    multipleFiles.value.splice(index, 1)

    const successFiles = multipleFiles.value
      .filter(f => f.status === 'success')
      .map(f => ({
        url: f.ossUrl,
        objectName: f.objectName,
        name: f.file.name,
        type: f.type,
        size: f.file.size
      }))

    emit('update:uploadedFiles', successFiles)
  }
}

const clearAllFiles = () => {
  multipleFiles.value.forEach(f => {
    if (f.url) {
      URL.revokeObjectURL(f.url)
    }
  })

  multipleFiles.value = []

  emit('update:uploadedFiles', [])
}

const handleFileChange = (file) => {
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isLt5M) {
    ElMessage.error('文件大小不能超过 5MB!');
    return false;
  }

  uploadedFile.value = file.raw || file;
  uploadedFileUrl.value = URL.createObjectURL(uploadedFile.value);

  emit('update:selectedFile', uploadedFile.value);
  emit('update:selectedFileUrl', uploadedFileUrl.value);

  return false;
}

const clearFile = () => {
  uploadedFile.value = null;
  uploadedFileUrl.value = '';
  emit('update:selectedFile', null);
  emit('update:selectedFileUrl', '');
}

const triggerFileUpload = () => {
  // ElMessage.success('敬请期待')
  if (fileInputRef.value) {
    fileInputRef.value.click();
  }
}

const inputUrl = ref('')
const isValidUrl = ref(true)
const urlErrorMessage = ref('')

const validateUrl = (url) => {
  if (!url) {
    isValidUrl.value = false;
    urlErrorMessage.value = '请输入URL';
    return false;
  }

  try {
    new URL(url);
    isValidUrl.value = true;
    urlErrorMessage.value = '';
    return true;
  } catch (e) {
    isValidUrl.value = false;
    urlErrorMessage.value = '请输入有效的URL';
    return false;
  }
}

const updateUrl = (url) => {
  inputUrl.value = url;
  validateUrl(url);

  if (isValidUrl.value) {
    emit('update:selectedUrl', url);
    activeSourceType.value = SOURCE_TYPES.URL;
  }
}

const updateValue = (value) => {
  // 如果正在执行打字机效果，直接更新值
  if (isTyping.value) {
    emit('update:modelValue', value)
    return
  }

  // 如果是用户输入（而不是程序设置），不使用打字机效果
  emit('update:modelValue', value)
}

const addSuggestion = (suggestion) => {
  // 使用打字机效果展示建议内容
  typewriterEffect(suggestion)
}

const refreshSuggestions = () => {
  emit('refresh-suggestions')
}

const isFormValid = computed(() => {
  return props.modelValue.trim().length > 0
})

const groupedSuggestions = computed(() => {
  const groups = {};

  const defaultCategory = '其他';

  props.suggestions.forEach(suggestion => {
    const category = suggestion.category || defaultCategory;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(suggestion);
  });

  return Object.entries(groups).map(([category, items]) => ({
    category,
    items
  }));
});

const goToCreativePrefs = () => {
  router.push({
    path: '/profile',
    query: { showCreativePrefs: 'true' }
  })
}

const handleStartCreation = () => {
  if (isFormValid.value) {
    emit('start-creation')
  }
}

// 添加计算属性获取风格名称
const getStyleName = computed(() => {
  return props.styleName || '风格自动'
})

// 添加计算属性获取音色名称
const getVoiceName = computed(() => {
  return props.voiceName || '音色自动'
})

// 添加模型名称计算属性
const getModelName = computed(() => {
  return props.modelName || '暂未选择模型'
})

// 添加outputRatio计算属性
const outputRatio = computed(() => {
  return props.outputRatio || '暂未选择尺寸'
})

// 判断是否有选中的设置
const hasSelectedSettings = computed(() => {
  return props.selectedStyle || props.selectedVoice || props.selectedModel || outputRatio.value
})

// 打开设置模块
const openSetting = (module) => {
  emit('open-setting', module)
}
</script>

<style scoped>
.creative-input {
  margin-top: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-height: 100%;
  padding: 0 20px;
  box-sizing: border-box;
}

.input-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 16px;
  border-radius: 22px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.03);
  background-color: #f9fafcc7;
  gap: 16px;
}


.bt_content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}

.bt_content_add {
  padding-left: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}

.multiple-files-display {
  display: flex;
  align-items: center;
  color: #409eff;
  font-size: 14px;
  padding: 0 8px;
}

.files-count {
  margin-right: 5px;
}

.upload-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.upload-button-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.custom-upload-btn {
  transform: rotate(-8deg);
  background-color: #ffffff6f;
  border: 1px solid #40a0ff46;
  border-radius: 4px;
  color: #40a0ff78;
  width: 54px;
  aspect-ratio: 3/4;
  /* height: 30px; */
  padding: 0;
  transition: all 0.3s ease;
  cursor: pointer;
}

.custom-upload-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.25);
  background-color: #f0f9ff;
}

.custom-upload-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.15);
}

.custom-upload-btn .el-icon {
  font-size: 16px;
}

body.dark .custom-upload-btn {
  background-color: #1a1a1a;
  border-color: #409eff;
  color: #67a9ff;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

body.dark .custom-upload-btn:hover {
  background-color: #2a2a2a;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

body.dark .multiple-files-display {
  color: #a0cfff;
}

.send-button {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  gap: 6px;
  transition: all 0.3s;
}

.send-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.send-icon {
  font-size: 16px;
}

body.dark .send-button {
  background-color: var(--color-primary, #409eff);
}

.input-wrapper:hover {
  border-color: #409eff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

body.dark .input-wrapper {
  border-color: var(--border-color);
  background-color: var(--bg-secondary);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

body.dark .input-wrapper:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.style-badge {
  position: absolute;
  top: -10px;
  right: -12px;
  background-color: rgba(0, 0, 0, 0.081);
  color: rgb(10, 97, 248);
  padding: 2px 8px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
  backdrop-filter: blur(4px);
  opacity: 0.8;
  transition: all 0.3s ease;
}

body.dark .style-badge {
  background-color: rgba(255, 255, 255, 0.142);
  color: rgb(255, 255, 255);
}

.section-header {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 24px;
  width: 100%;
}

.prefs-btn {
  margin-left: auto;
  border-radius: 8px;
  font-size: 13px;
  transition: all 0.3s ease;
}

.prefs-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.15);
}

.prefs-btn .el-icon {
  margin-right: 4px;
  font-size: 14px;
}

.section-header h2 {
  font-size: 42px;
  font-weight: 700;
  color: #303133;
  margin: 0;
  position: relative;
  padding-bottom: 10px;
  transition: color 0.3s;
  letter-spacing: -0.5px;
}

.title-with-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  position: relative;
}

.title-icon {
  font-size: 28px;
  color: #6366f1;
  animation: icon-glow 2s ease-in-out infinite alternate;
}

.gradient-text {
  background: linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  display: inline-block;
  animation: text-shimmer 4s ease-in-out infinite;
}

body.dark .gradient-text {
  background: linear-gradient(135deg, #818cf8, #a78bfa, #f472b6);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
}

.title-with-icon::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
  border-radius: 3px;
  animation: line-expand 0.6s ease-out forwards;
}

@keyframes icon-glow {
  0% {
    text-shadow: 0 0 5px rgba(99, 102, 241, 0.3);
    transform: scale(1);
  }

  100% {
    text-shadow: 0 0 15px rgba(99, 102, 241, 0.6);
    transform: scale(1.1);
  }
}

@keyframes text-shimmer {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

@keyframes line-expand {
  0% {
    width: 0;
    opacity: 0;
  }

  100% {
    width: 80px;
    opacity: 1;
  }
}

body.dark .section-header h2 {
  color: var(--text-primary);
}

body.dark .title-icon {
  color: #818cf8;
}

body.dark .title-with-icon::after {
  background: linear-gradient(90deg, #818cf8, #a78bfa, #f472b6);
}

/* .section-header h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  transform: none;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  border-radius: 3px;
} */

:deep(.el-textarea__inner) {
  border: 1px solid transparent;
  padding: 8px;
  font-size: 16px;
  background-color: transparent;
  transition: all 0.3s;
  box-shadow: none;
  border-color: transparent;
}

:deep(.el-input__count) {
  bottom: 12px;
  right: 14px;
}

body.dark :deep(.el-textarea__inner) {
  /* border-color: transparent;
  background-color: transparent;
  box-shadow: none; */
  /* color: var(--text-primary); */
}

:deep(.el-textarea__inner:focus) {
  border-color: transparent;
  box-shadow: none;
  color: var(--text-primary);
}

body.dark :deep(.el-textarea__inner:focus) {
  border-color: transparent;
  box-shadow: none;
}

:deep(.el-textarea__word-count) {
  color: #909399;
  font-size: 14px;
  padding: 8px 12px;
  transition: color 0.3s;
}

body.dark :deep(.el-textarea__word-count) {
  color: var(--text-secondary);
}

.input-suggestions {
  /* padding-top: 24px; */
  width: 100%;
  overflow-x: auto;
  padding: 0 16px;
  box-sizing: border-box;
  position: relative;
}

.suggestions-categories {
  display: flex;
  justify-content: center;
  padding: 2px;
  /* flex-direction: wrap; */
  /* flex-direction: column; */
  /* gap: 4px; */
  /* width: 100%; */
  /* box-sizing: border-box; */
}

.suggestion-category {
  display: flex;
  flex-direction: wrap;
  gap: 4px;
}

.category-title {
  width: 80px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  position: relative;
  padding-bottom: 6px;
  transition: color 0.3s;
  text-align: left;
}

body.dark .category-title {
  color: var(--text-primary);
}

.category-title::after {
  content: '';
  position: absolute;
  top: 30px;
  left: 0;
  width: 10px;
  height: 2px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  border-radius: 2px;
}

.suggestion-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 4px;
  /* flex: 1; */
  min-width: min-content;
}

.suggestion-tag {
  padding: 2px 8px;
  text-align: left;
  background-color: #e7ecf4;
  border-radius: 16px;
  font-size: 12px;
  color: #4e4e50bc;
  cursor: pointer;
  transition: all 0.25s ease;
  border: 1px solid transparent;
  position: relative;
  gap: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

body.dark .suggestion-tag {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
}

.suggestion-tag:hover {
  background-color: #ecf5ff;
  color: #409eff;
  border-color: #d9ecff;
  transform: scale(1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

body.dark .suggestion-tag:hover {
  background-color: rgba(64, 158, 255, 0.15);
  color: #67a9ff;
  border-color: rgba(64, 158, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.suggestions-loading {
  width: 100%;
  padding: 10px 0;
}

.suggestions-empty {
  width: 100%;
  text-align: center;
  color: #909399;
  padding: 20px 0;
  font-size: 14px;
}

body.dark .suggestions-empty {
  color: var(--text-secondary);
}

.suggestions-header {
  /* position: absolute;
  bottom: 10px;
  right: 10px; */
  /* margin-top: 16px; */
  /* text-align: right; */
  display: flex;
  justify-content: center;
  align-items: center;
}

.refresh-btn {
  font-size: 13px;
  transition: all 0.3s ease;
  border-radius: 8px;
}

/* .refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.15);
} */

.refresh-btn .el-icon {
  margin-right: 4px;
}

body.dark .refresh-btn {
  background-color: rgba(64, 158, 255, 0.1);
}


/* 自定义滚动条样式，参考ShotGeneration.vue */
:deep(.el-textarea__inner::-webkit-scrollbar) {
  width: 0px;
}

:deep(.el-textarea__inner::-webkit-scrollbar-thumb) {
  background-color: #6365f139;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark :deep(.el-textarea__inner::-webkit-scrollbar-thumb) {
  background-color: rgba(99, 102, 241, 0.2);
}

:deep(.el-textarea__inner::-webkit-scrollbar-track) {
  background-color: #f1f5f9;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark :deep(.el-textarea__inner::-webkit-scrollbar-track) {
  background-color: var(--bg-tertiary);
}


/* 来源选择区域样式 */
.source-selection {
  margin-top: 16px;
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  /* background-color: #f9fafc; */
  /* box-shadow: 0 2px 12px rgba(0, 0, 0, 0.03); */
  transition: background-color 0.3s, box-shadow 0.3s;
  /* border: 1px solid #e4e7ed; */
}

body.dark .source-selection {
  /* background-color: var(--bg-secondary); */
  /* border-color: var(--border-color); */
  /* box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); */
}

/* 来源选项卡样式 */
.source-tabs {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
  transition: border-color 0.3s;
}

body.dark .source-tabs {
  border-color: var(--border-color);
}

.source-tab {
  flex: 1;
  padding: 14px 16px;
  text-align: center;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border-bottom: 2px solid transparent;
}

body.dark .source-tab {
  color: var(--text-secondary);
}

.source-tab .el-icon {
  font-size: 16px;
}

.source-tab:hover {
  color: #409eff;
  background-color: rgba(64, 158, 255, 0.05);
}

body.dark .source-tab:hover {
  background-color: rgba(64, 158, 255, 0.1);
}

.source-tab.active {
  color: #409eff;
  border-bottom-color: #409eff;
  background-color: rgba(64, 158, 255, 0.08);
}

body.dark .source-tab.active {
  background-color: rgba(64, 158, 255, 0.15);
}

/* 来源内容区域样式 */
.source-content {
  padding: 0px;
}

.source-panel {
  /* min-height: 80px; */
}

/* 摘要选择面板样式 */
.empty-summary {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: #909399;
  font-size: 14px;
  text-align: center;
}

body.dark .empty-summary {
  color: var(--text-secondary);
}

/* 文件上传面板样式 */
.file-upload-container {
  width: 100%;
}

.upload-area {
  /* width: 100%; */
  height: 180px;
  border: 1px dashed #c0c4cc;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: rgba(64, 158, 255, 0.02);
  margin: 16px;
}

body.dark .upload-area {
  border-color: var(--border-color);
  background-color: rgba(64, 158, 255, 0.05);
}

.upload-area:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.05);
}

body.dark .upload-area:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
}

.upload-icon {
  font-size: 28px;
  color: #409eff;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 4px;
}

body.dark .upload-text {
  color: var(--text-primary);
}

.upload-hint {
  font-size: 12px;
  color: #909399;
}

body.dark .upload-hint {
  color: var(--text-secondary);
}

/* 上传进度指示器 */
.upload-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  gap: 12px;
}

.upload-progress-text {
  color: #409eff;
  font-size: 14px;
  font-weight: 500;
}

/* 文件列表样式 */
.files-list {
  /* margin-top: 16px; */
  /* border-radius: 8px; */
  /* border: 1px solid #e4e7ed; */
  overflow: hidden;
  transition: border-color 0.3s;
}

body.dark .files-list {
  border-color: var(--border-color);
}

.files-list-header {
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.3s, border-color 0.3s;
}

body.dark .files-list-header {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

.files-list-title {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
  transition: color 0.3s;
}

body.dark .files-list-title {
  color: var(--text-secondary);
}

.files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  padding: 16px;
}

/* 文件项样式 */
.file-item {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
  background-color: #ffffff;
  position: relative;
}

body.dark .file-item {
  border-color: var(--border-color);
  background-color: var(--bg-tertiary);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.file-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #c0c4cc;
}

body.dark .file-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: var(--border-color-light, #4c4c4c);
}

.file-item.is-uploading {
  border-color: #409eff;
}

.file-item.is-error {
  border-color: #f56c6c;
}

.file-item-preview {
  height: 120px;
  position: relative;
  overflow: hidden;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}

body.dark .file-item-preview {
  background-color: var(--bg-quaternary, #2d3748);
}

.file-preview-image,
.file-preview-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-preview-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(64, 158, 255, 0.1);
  color: #409eff;
}

body.dark .file-preview-icon {
  background-color: rgba(64, 158, 255, 0.2);
}

.file-preview-icon .el-icon {
  font-size: 30px;
}

.file-status-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  transition: background-color 0.3s;
}

body.dark .file-status-overlay {
  background-color: rgba(45, 55, 72, 0.8);
}

.error-icon {
  font-size: 40px;
  color: #f56c6c;
}

.file-item-info {
  padding: 6px;
  position: relative;
}

.file-item-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
  transition: color 0.3s;
}

body.dark .file-item-name {
  color: var(--text-primary);
}

.file-item-size {
  font-size: 12px;
  color: #909399;
  transition: color 0.3s;
}

body.dark .file-item-size {
  color: var(--text-tertiary);
}

.file-item-actions {
  position: absolute;
  top: -16px;
  right: 8px;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.file-item:hover .file-item-actions {
  opacity: 1;
}

/* 添加更多文件按钮 */
.file-item.add-more-files {
  border: 1px dashed #c0c4cc;
  box-shadow: none;
  cursor: pointer;
  background-color: transparent;
}

body.dark .file-item.add-more-files {
  border-color: var(--border-color);
}

.file-item.add-more-files:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.05);
}

body.dark .file-item.add-more-files:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
}

.add-files-button {
  width: 100%;
  height: 100%;
  display: flex;
  box-sizing: border-box;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #909399;
  transition: color 0.3s;
}

body.dark .add-files-button {
  color: var(--text-tertiary);
}

.add-files-button .el-icon {
  font-size: 24px;
}

.file-item.add-more-files:hover .add-files-button {
  color: #409eff;
}

body.dark .file-item.add-more-files:hover .add-files-button {
  color: #67a9ff;
}

/* 文件预览样式 (旧版) */
.file-preview {
  padding: 16px;
  border-radius: 8px;
  background-color: rgba(64, 158, 255, 0.05);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.file-preview-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-preview-image {
  /* width: 80px;
  height: 80px; */
  object-fit: cover;
  border-radius: 0px;
  background-color: #f5f7fa;
  transition: transform 0.3s;
}

body.dark .file-preview-image {
  background-color: var(--bg-tertiary);
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  word-break: break-all;
}

body.dark .file-name {
  color: var(--text-primary);
}

.file-size {
  font-size: 14px;
  color: #909399;
}

body.dark .file-size {
  color: var(--text-secondary);
}

.file-actions {
  display: flex;
  justify-content: flex-end;
}

/* URL输入面板样式 */
.url-input-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
}

:deep(.el-input.is-error .el-input__wrapper) {
  box-shadow: 0 0 0 1px #f56c6c inset;
}

.url-error {
  color: #f56c6c;
  font-size: 12px;
  margin-top: -8px;
}

.url-preview {
  padding: 12px 16px;
  background-color: rgba(64, 158, 255, 0.05);
  border-radius: 8px;
  margin-top: 8px;
}

.url-preview-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.url-preview-content .el-icon {
  font-size: 18px;
  color: #409eff;
}

.url-text {
  font-size: 14px;
  color: #409eff;
  word-break: break-all;
}

.selected-summary {
  /* margin-top: 14px; */
  padding: 16px;
  background-color: #f9fafc;
  border-radius: 8px;
  transition: background-color 0.3s;
}

body.dark .selected-summary {
  background-color: var(--bg-tertiary);
}

.summary-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-size: 14px;
  color: #606266;
  margin-right: 12px;
  white-space: nowrap;
  transition: color 0.3s;
}

body.dark .summary-label {
  color: var(--text-secondary);
}

.summary-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.creation-options {
  margin-top: 24px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.option-item {
  color: #606266;
  transition: color 0.3s;
}

body.dark .option-item {
  color: var(--text-secondary);
}

/* 已选设置样式 */
.selected-settings {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  flex: 1;
  margin-right: 10px;
  justify-content: flex-start;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  border-radius: 8px;
  font-size: 12px;
  background-color: rgba(64, 158, 255, 0.1);
  color: #409eff;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(64, 158, 255, 0.2);
  cursor: pointer;
  position: relative;
}

/* .setting-item::after {
  content: '';
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgba(64, 158, 255, 0.5);
  opacity: 0;
  transition: opacity 0.3s;
} */

.setting-item:hover {
  background-color: rgba(64, 157, 250, 0.255);
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.273);
}

.setting-item:hover::after {
  opacity: 1;
}

body.dark .setting-item {
  background-color: rgba(64, 158, 255, 0.15);
  color: #67a9ff;
  border-color: rgba(64, 158, 255, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

body.dark .setting-item:hover {
  background-color: rgba(64, 158, 255, 0.25);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.setting-text {
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.setting-item .el-icon {
  font-size: 14px;
}

@media (max-width: 768px) {
  .section-header h2 {
    font-size: 24px;
  }

  .prefs-btn {
    font-size: 12px;
    padding: 6px 10px;
  }

  .suggestion-tags {
    gap: 8px;
  }

  .suggestion-tag {
    padding: 6px 12px;
    font-size: 13px;
  }

  .category-title {
    font-size: 15px;
  }

  /* 移动端来源选择区域样式 */
  .source-tab {
    padding: 12px 8px;
    font-size: 13px;
    gap: 4px;
  }

  .source-tab .el-icon {
    font-size: 14px;
  }

  .source-content {
    padding: 12px;
  }

  .upload-area {
    height: 100px;
  }

  .upload-icon {
    font-size: 24px;
  }

  .upload-text {
    font-size: 14px;
  }

  .file-preview-image {
    width: 60px;
    height: 60px;
  }

  .file-name {
    font-size: 14px;
  }

  .file-size {
    font-size: 12px;
  }

  /* 移动端文件网格布局调整 */
  .files-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 12px;
  }

  .file-item-preview {
    height: 100px;
  }

  .selected-settings {
    gap: 6px;
  }

  .setting-item {
    padding: 1px 6px;
    font-size: 11px;
  }

  .setting-text {
    max-width: 80px;
  }
}

@media (max-width: 480px) {
  .section-header h2 {
    font-size: 20px;
  }

  .suggestion-tags {
    justify-content: flex-start;
  }

  .category-title {
    font-size: 14px;
  }

  /* 小屏幕来源选择区域样式 */
  .source-tab {
    padding: 10px 4px;
    font-size: 12px;
  }

  .source-tab span {
    display: none;
    /* 在超小屏幕上只显示图标 */
  }

  .source-tab .el-icon {
    font-size: 16px;
    margin: 0;
  }

  .source-content {
    padding: 10px;
  }

  .upload-area {
    height: 90px;
  }

  .upload-text {
    font-size: 13px;
  }

  .upload-hint {
    font-size: 11px;
  }

  .file-preview-content {
    flex-direction: column;
    align-items: flex-start;
  }

  .file-preview-image {
    width: 100%;
    height: auto;
    max-height: 120px;
  }

  /* 超小屏幕文件网格布局调整 */
  .files-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
    padding: 10px;
  }

  .file-item-preview {
    height: 80px;
  }

  .file-item-info {
    padding: 8px;
  }

  .file-item-name {
    font-size: 12px;
  }

  .file-item-size {
    font-size: 10px;
  }

  .add-files-button {
    font-size: 12px;
  }

  .add-files-button .el-icon {
    font-size: 20px;
  }

  .selected-settings {
    gap: 4px;
  }

  .setting-item {
    padding: 1px 4px;
    font-size: 10px;
  }

  .setting-text {
    max-width: 60px;
  }
}

body.dark :deep(.el-textarea__inner:focus) {
  border-color: transparent;
  box-shadow: none;
}

/* 打字机效果样式 */
@keyframes cursor-blink {

  0%,
  100% {
    border-right-color: transparent;
  }

  50% {
    border-right-color: #409eff;
  }
}

.typewriter-input :deep(.el-textarea__inner) {
  caret-color: #409eff;
}

/* 设置项变化动效 */
@keyframes setting-highlight {
  0% {
    transform: scale(1);
  }

  25% {
    transform: scale(1.1);
  }

  50% {
    transform: scale(1);
    background-color: rgba(64, 158, 255, 0.3);
  }

  75% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

.setting-changed {
  animation: setting-highlight 0.8s ease-in-out;
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.5);
  background-color: rgba(64, 158, 255, 0.2);
}

body.dark .setting-changed {
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.7);
  background-color: rgba(64, 158, 255, 0.3);
}

/* multiple-file-container 样式 */
.multiple-file-container {
  display: flex;
  flex-direction: row;
  /* grid-template-columns: repeat(auto-fill, minmax(80px, 1fr)); */
  gap: 6px;
  /* padding: 8px 8px 0px 8px; */
  box-sizing: border-box;
}

.multiple-file-item {
  border-radius: 4px;
  /* transform: rotate(-10deg); */
  overflow: hidden;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
  background-color: #ffffff;
  position: relative;
}

.multiple-file-item.is-uploading {
  border-color: #409eff;
}

.multiple-file-item.is-error {
  border-color: #f56c6c;
}

body.dark .multiple-file-item {
  border-color: var(--border-color);
  background-color: var(--bg-tertiary);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.multiple-file-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #c0c4cc;
}

.multiple-file-item:hover .multiple-file-item-info {
  opacity: 1;
}

.multiple-file-item:hover .file-preview-image {
  transform: scale(1.1);
}

body.dark .multiple-file-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: var(--border-color-light, #4c4c4c);
}

.multiple-file-item-preview {
  width: 50px;
  aspect-ratio: 3/4;
  position: relative;
  overflow: hidden;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}

body.dark .multiple-file-item-preview {
  background-color: var(--bg-quaternary, #2d3748);
}

.multiple-file-item-preview img,
.multiple-file-item-preview .file-preview-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-preview-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(64, 158, 255, 0.1);
  color: #409eff;
}

body.dark .file-preview-icon {
  background-color: rgba(64, 158, 255, 0.2);
}

.file-preview-icon .el-icon {
  font-size: 20px;
}

.file-status-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  transition: background-color 0.3s;
}

body.dark .file-status-overlay {
  background-color: rgba(45, 55, 72, 0.8);
}

.error-icon {
  font-size: 24px;
  color: #f56c6c;
}

.multiple-file-item-info {
  width: 100%;
  position: absolute;
  bottom: 0px;
  left: 0px;
  padding: 4px 0px;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgb(0, 0, 0));
  /* position: relative; */
  opacity: 0;
}

.multiple-file-item-name {
  font-size: 12px;
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
  transition: color 0.3s;
}

body.dark .multiple-file-item-name {
  color: var(--text-primary);
}

.multiple-file-item-size {
  font-size: 10px;
  color: #ffffff;
  transition: color 0.3s;
}

body.dark .multiple-file-item-size {
  color: var(--text-tertiary);
}

.multiple-file-item-actions {
  position: absolute;
  top: 0px;
  right: 0px;
  /* transform: translateY(-50%); */
  opacity: 0;
  transition: opacity 0.3s ease;
}

.multiple-file-item:hover .multiple-file-item-actions {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .multiple-file-container {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
  }

  .multiple-file-item-preview {
    height: 70px;
  }

  .multiple-file-item-name {
    font-size: 11px;
  }

  .multiple-file-item-size {
    font-size: 9px;
  }
}

@media (max-width: 480px) {
  .multiple-file-container {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 6px;
  }

  .multiple-file-item-preview {
    height: 60px;
  }

  .multiple-file-item-name {
    font-size: 10px;
  }

  .multiple-file-item-size {
    font-size: 8px;
  }
}

/* DropdownPanel 内容样式 */
.dropdown-content-wrapper {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow-y: auto;
}

body.dark .dropdown-content-wrapper {
  background: var(--bg-secondary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}
</style>