<template>
  <div class="voice-selection">
    <!-- <div class="section-header">
      <h3>选择旁白音色</h3>
    </div> -->

    <!-- 加载中状态 -->
    <div v-if="isLoading" class="voice-loading">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载音色列表中...</div>
    </div>
    
    <!-- 空数据状态 -->
    <div v-else-if="voices.length === 0" class="voice-empty">
      <i class="el-icon-warning-outline"></i>
      <div>暂无可用音色</div>
    </div>
    
    <!-- 正常列表 - 使用 tabs 分类显示 -->
    <div v-else>
      <!-- 添加分类标签页 -->
      <el-tabs v-model="activeCategory" class="voice-category-tabs voice-selector-tabs" @tab-click="handleTabClick">
        <!-- 最近标签页 -->
        <el-tab-pane label="最近" name="recent"></el-tab-pane>
        
        <!-- 男声标签页 -->
        <el-tab-pane label="男声" name="male"></el-tab-pane>
        
        <!-- 女声标签页 -->
        <el-tab-pane label="女声" name="female"></el-tab-pane>
        
        <!-- 我的定制标签页 -->
        <el-tab-pane label="我的定制" name="custom"></el-tab-pane>
      </el-tabs>
      
      <!-- 统一的音色列表区域 -->
      <div ref="voiceListContainer" class="voice-list">
        <!-- 当是"我的定制"标签页且没有定制音色时显示提示和入口 -->
        <div v-if="activeCategory === 'custom' && filteredVoices.length === 0" class="custom-voice-empty">
          <div class="custom-voice-icon">
            <i class="el-icon-magic-stick"></i>
          </div>
          <div class="custom-voice-title">暂无定制音色</div>
          <div class="custom-voice-desc">您可以定制专属于您的AI配音</div>
          <button class="custom-voice-btn" @click="showCustomVoiceUpload">
            <span class="icon-plus"></span>
            定制新音色
          </button>
          <!-- <div class="custom-voice-hint">功能敬请期待</div> -->
        </div>
        
        <!-- 使用自定义音色上传对话框组件 -->
        <CustomVoiceDialog 
          v-model:visible="showUploadDialog" 
          @upload-success="handleCustomVoiceSuccess" 
        />
        
        <!-- 音色卡片列表 -->
        <div v-if="!(activeCategory === 'custom' && filteredVoices.length === 0)" class="voice-cards-container">
          <div v-for="(voice, index) in filteredVoices" :key="index" class="voice-item"
            :class="{ active: (props.modelValue || props.selectedVoice) === voice.id }"
            :style="{ 'flex-basis': cardFlexBasis, 'width': `calc(${100 / columnCount}% - ${15}px)` }"
            @click="selectVoice(voice.id)">
            <div class="voice-info">
              <div class="voice-name">
                {{ voice.name }}
              </div>
              <div class="voice-language">
                <el-tag size="small" :type="voice.sex === 1 ? 'primary' : 'danger'" effect="plain">
                  {{ voice.sex === 1 ? '男声' : '女声' }}
                </el-tag>
                {{ voice.languageName }}
              </div>
            </div>
            <div class="voice-actions">
              <!-- 编辑按钮 - 仅在"我的定制"标签页显示 -->
              <el-button
                v-if="activeCategory === 'custom'"
                class="edit-name-btn"
                type="primary"
                size="small"
                circle
                @click.stop="openEditNameDialog(voice)"
              >
                <el-icon :size="12">
                  <EditPen />
                </el-icon>
              </el-button>
              <!-- 删除按钮 - 仅在"我的定制"标签页显示 -->
              <el-button
                v-if="activeCategory === 'custom'"
                class="delete-voice-btn"
                type="danger"
                size="small"
                circle
                @click.stop="openDeleteConfirmDialog(voice)"
              >
                <el-icon :size="12">
                  <Delete />
                </el-icon>
              </el-button>
              <div class="voice-play" 
                :class="{ 'playing': isPlaying(voice.id) }" 
                @click.stop="playVoice(voice)">
                <VideoPause v-if="isPlaying(voice.id)" class="play-icon" />
                <VideoPlay v-else class="play-icon" />
                <span class="wave-animation" v-if="isPlaying(voice.id)">
                  <span class="wave-bar"></span>
                  <span class="wave-bar"></span>
                  <span class="wave-bar"></span>
                  <span class="wave-bar"></span>
                </span>
              </div>
            </div>
          </div>
          
          <!-- 定制新音色卡片 - 仅在"我的定制"标签页且有音色时显示 -->
          <div v-if="activeCategory === 'custom' && filteredVoices.length > 0" 
            class="voice-item add-voice-item"
            :style="{ 'flex-basis': cardFlexBasis, 'width': `calc(${100 / columnCount}% - ${15}px)` }"
            @click="showCustomVoiceUpload">
            <div class="add-voice-content">
              <div class="add-voice-icon">
                <i class="el-icon-plus"></i>
              </div>
              <div class="add-voice-text">定制新音色</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 编辑音色名称对话框 -->
    <el-dialog
      v-model="showEditNameDialog"
      title="修改音色名称"
      width="30%"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      center
    >
      <div class="edit-name-content">
        <el-input
          v-model="editingVoiceName"
          placeholder="请输入新的音色名称"
          maxlength="20"
          show-word-limit
        ></el-input>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditNameDialog = false">取消</el-button>
          <el-button type="primary" :disabled="!editingVoiceName.trim()" :loading="isSaving" @click="saveVoiceName">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 删除音色确认对话框 -->
    <el-dialog
      v-model="showDeleteDialog"
      title=""
      width="300px"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      center
    >
      <div class="delete-voice-content">
        <p>确定要删除音色 "{{ deletingVoice?.name }}" 吗？</p>
        <p class="delete-warning">删除后将无法恢复，请谨慎操作。</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDeleteDialog = false">取消</el-button>
          <el-button type="danger" :loading="isDeleting" @click="deleteVoice">
            确认删除
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
import { VideoPause, VideoPlay, EditPen, Delete } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import CustomVoiceDialog from '../CustomVoiceDialog.vue';
import { updateSoundName, deleteSound } from '@/api/auth.js';

const props = defineProps({
  voices: {
    type: Array,
    default: () => []
  },
  modelValue: {
    type: Number,
    default: null
  },
  selectedVoice: {
    type: Number,
    default: null
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  initialColumns: {
    type: Number,
    default: 2 // 默认初始列数
  }
});

const emit = defineEmits(['update:modelValue', 'update:selectedVoice', 'refresh-voices']);

// 当前选中的分类 - 初始化时检查是否有最近使用的数据
const getInitialCategory = () => {
  try {
    const stored = localStorage.getItem('recent_voices');
    const recentIds = stored ? JSON.parse(stored) : [];
    return recentIds.length > 0 ? 'recent' : 'male';
  } catch (error) {
    return 'male';
  }
};

const activeCategory = ref(getInitialCategory());

// 存储最近使用的音色
const recentVoices = ref([]);

// 编辑音色名称相关状态
const showEditNameDialog = ref(false);
const editingVoiceId = ref(null);
const editingVoiceName = ref('');
const isSaving = ref(false);

// 删除音色相关状态
const showDeleteDialog = ref(false);
const deletingVoice = ref(null);
const isDeleting = ref(false);

// 打开编辑名称对话框
const openEditNameDialog = (voice) => {
  editingVoiceId.value = voice.id;
  editingVoiceName.value = voice.name;
  showEditNameDialog.value = true;
};

// 保存音色名称
const saveVoiceName = async () => {
  if (!editingVoiceName.value.trim() || !editingVoiceId.value) return;
  
  try {
    isSaving.value = true;
    
    // 调用API更新音色名称
    await updateSoundName({
      soundId: editingVoiceId.value,
      name: editingVoiceName.value.trim()
    });
    
    // 更新成功
    ElMessage.success('音色名称修改成功');
    
    // 关闭对话框
    showEditNameDialog.value = false;
    
    // 刷新音色列表
    emit('refresh-voices');
    
  } catch (error) {
    console.error('修改音色名称失败:', error);
    ElMessage.error('修改音色名称失败: ' + (error.message || '未知错误'));
  } finally {
    isSaving.value = false;
  }
};

// 打开删除确认对话框
const openDeleteConfirmDialog = (voice) => {
  deletingVoice.value = voice;
  showDeleteDialog.value = true;
};

// 删除音色
const deleteVoice = async () => {
  if (!deletingVoice.value || !deletingVoice.value.id) return;
  
  try {
    isDeleting.value = true;
    
    // 调用API删除音色
    await deleteSound(deletingVoice.value.id);
    
    // 删除成功
    ElMessage.success(`音色 "${deletingVoice.value.name}" 删除成功`);
    
    // 关闭对话框
    showDeleteDialog.value = false;
    deletingVoice.value = null;
    
    // 刷新音色列表
    emit('refresh-voices');
    
  } catch (error) {
    console.error('删除音色失败:', error);
    ElMessage.error('删除音色失败: ' + (error.message || '未知错误'));
  } finally {
    isDeleting.value = false;
  }
};

// 本地存储键名
const RECENT_VOICES_KEY = 'recent_voices';

// 从本地存储获取最近使用的音色ID列表
const getRecentVoiceIds = () => {
  try {
    const stored = localStorage.getItem(RECENT_VOICES_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('获取最近使用音色失败:', error);
    return [];
  }
};

// 保存最近使用的音色ID列表到本地存储
const saveRecentVoiceIds = (voiceIds) => {
  try {
    localStorage.setItem(RECENT_VOICES_KEY, JSON.stringify(voiceIds));
  } catch (error) {
    console.error('保存最近使用音色失败:', error);
  }
};

// 添加音色到最近使用列表
const addToRecentVoices = (voiceId) => {
  const recentIds = getRecentVoiceIds();

  // 移除已存在的相同ID（如果有）
  const filteredIds = recentIds.filter(id => id !== voiceId);

  // 将新ID添加到开头
  const newRecentIds = [voiceId, ...filteredIds];

  // 只保留最近的12个
  const limitedIds = newRecentIds.slice(0, 12);

  // 保存到本地存储
  saveRecentVoiceIds(limitedIds);

  // 更新最近使用的音色列表
  updateRecentVoices();
};

// 更新最近使用的音色列表
const updateRecentVoices = () => {
  const recentIds = getRecentVoiceIds();
  const recentVoicesList = [];

  // 根据ID顺序从所有音色中找到对应的音色对象
  recentIds.forEach(id => {
    const voice = props.voices.find(v => v.id === id);
    if (voice) {
      recentVoicesList.push(voice);
    }
  });

  recentVoices.value = recentVoicesList;

  // 如果当前在「最近」tab但没有最近使用的数据，自动切换到「男声」
  if (activeCategory.value === 'recent' && recentVoicesList.length === 0) {
    activeCategory.value = 'male';
  }
};

// 根据当前选中的分类过滤音色
const filteredVoices = computed(() => {
  if (activeCategory.value === 'recent') {
    return recentVoices.value;
  } else if (activeCategory.value === 'male') {
    return props.voices.filter(voice => voice.sex === 1 && voice.type === 1);
  } else if (activeCategory.value === 'female') {
    return props.voices.filter(voice => voice.sex === 2 && voice.type === 1);
  } else if (activeCategory.value === 'custom') {
    return props.voices.filter(voice => voice.type === 2);
  } else {
    return props.voices;
  }
});

// 自定义音色上传对话框显示状态
const showUploadDialog = ref(false);

// 显示自定义音色上传对话框
const showCustomVoiceUpload = () => {
  showUploadDialog.value = true;
};

// 处理自定义音色上传成功
const handleCustomVoiceSuccess = (voiceInfo) => {
  // 触发刷新音色列表事件
  emit('refresh-voices');
  
  // 切换到"我的定制"标签页
  activeCategory.value = 'custom';
  
  // 显示成功提示
  ElMessage.success(`音色 "${voiceInfo.voiceName}" 定制成功`);
  showUploadDialog.value = false;
};

// 当原始音色数组改变时更新最近使用音色
watch(() => props.voices, () => {
  updateRecentVoices();
}, { deep: true });

// 单一的音色列表容器引用
const voiceListContainer = ref(null);

const columnCount = ref(props.initialColumns);
const idealCardWidth = 270; // 理想的卡片宽度（像素）
const containerWidth = ref(0);

// 处理标签页点击事件，更新列数
const handleTabClick = () => {
  // 延迟一下，确保DOM已更新
  nextTick(() => {
    updateColumnCount();
  });
};

// 根据容器宽度更新列数
const updateColumnCount = () => {
  // 获取容器宽度
  if (voiceListContainer.value) {
    containerWidth.value = voiceListContainer.value.clientWidth;
  } else {
    // 如果容器还未渲染，使用窗口宽度减去一些边距作为估计值
    containerWidth.value = window.innerWidth - 40;
  }
  
  // 在窄屏上强制单列
  if (containerWidth.value <= 500) {
    columnCount.value = 1;
    return;
  }
  
  // 计算最佳列数（容器宽度除以理想卡片宽度，向下取整）
  let calculatedColumns = Math.floor(containerWidth.value / idealCardWidth);
  
  // 确保列数在合理范围内 - 即使容器很宽，也最多显示4列
  calculatedColumns = Math.max(1, Math.min(calculatedColumns, 4));
  
  // 更新列数
  columnCount.value = calculatedColumns;
  
  console.log('Container width:', containerWidth.value, 'Columns:', columnCount.value);
};

// 防抖函数
const debounce = (fn, delay) => {
  let timer = null;
  return function() {
    const context = this;
    const args = arguments;
    clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(context, args);
    }, delay);
  };
};

// 防抖处理的列数更新函数
const debouncedUpdateColumnCount = debounce(updateColumnCount, 200);

// 存储 ResizeObserver 实例的引用
let resizeObserver = null;

// 计算每个卡片的宽度
const cardFlexBasis = computed(() => {
  return `calc(${100 / columnCount.value}% - ${(columnCount.value - 1) * 15 / columnCount.value}px)`;
});

const selectVoice = (voiceId) => {
  emit('update:modelValue', voiceId);
  emit('update:selectedVoice', voiceId);

  // 添加到最近使用列表
  addToRecentVoices(voiceId);
};

// 当前播放的音频元素和ID
const currentAudio = ref(null);
const currentPlayingId = ref(null);

// 判断是否正在播放特定音色
const isPlaying = (voiceId) => {
  return currentPlayingId.value === voiceId && currentAudio.value && !currentAudio.value.paused;
};

// 播放音色示例
const playVoice = (voice) => {
  // 如果正在播放当前音色，则暂停
  if (isPlaying(voice.id)) {
    currentAudio.value.pause();
    currentAudio.value = null;
    currentPlayingId.value = null;
    return;
  }
  
  // 如果有其他正在播放的音频，先停止
  if (currentAudio.value) {
    currentAudio.value.pause();
    currentAudio.value = null;
    currentPlayingId.value = null;
  }
  
  // 创建新的音频元素并播放
  if (voice.audioUrl) {
    const audioUrl = voice.audioUrl;
    const audio = new Audio(audioUrl);
    
    // 设置事件监听
    audio.addEventListener('play', () => {
      currentPlayingId.value = voice.id;
    });
    
    audio.addEventListener('ended', () => {
      currentAudio.value = null;
      currentPlayingId.value = null;
    });
    
    audio.addEventListener('pause', () => {
      if (currentPlayingId.value === voice.id) {
        currentPlayingId.value = null;
      }
    });
    
    audio.addEventListener('error', () => {
      console.error('音频播放错误');
      currentAudio.value = null;
      currentPlayingId.value = null;
    });
    
    // 播放
    audio.play().catch(error => {
      console.error('播放失败:', error);
      currentAudio.value = null;
      currentPlayingId.value = null;
    });
    
    currentAudio.value = audio;
  }
};

// 组件挂载后初始化
onMounted(() => {
  // 初始化最近使用的音色列表
  updateRecentVoices();

  // 初始化列数并添加窗口大小变化监听
  nextTick(() => {
    updateColumnCount();
    window.addEventListener('resize', debouncedUpdateColumnCount);

    // 等待下一个渲染周期，确保容器已经渲染
    nextTick(() => {
      // 为容器设置 ResizeObserver
      if (voiceListContainer.value && 'ResizeObserver' in window) {
        resizeObserver = new ResizeObserver(() => {
          // 当容器大小变化时，更新列数
          debouncedUpdateColumnCount();
        });
        resizeObserver.observe(voiceListContainer.value);
        console.log('ResizeObserver attached to voice list container');
      }
    });
  });
});

// 组件卸载前清理事件监听
onBeforeUnmount(() => {
  // 停止正在播放的音频
  if (currentAudio.value) {
    currentAudio.value.pause();
    currentAudio.value = null;
  }
  
  // 移除窗口大小变化监听
  window.removeEventListener('resize', debouncedUpdateColumnCount);
  
  // 清理 ResizeObserver
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
});
</script>

<style scoped>
.voice-selection {
  margin-bottom: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.section-header h3 {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  transition: color 0.3s;
}

body.dark .section-header h3 {
  color: var(--text-primary);
}

/* 分类标签页样式 */
.voice-category-tabs {
  width: 100%;
}

/* 添加更具体的选择器以覆盖父组件样式 */
.voice-selector-tabs :deep(.el-tabs__header) {
  margin-bottom: 8px !important;
  position: relative !important;
}

.voice-selector-tabs :deep(.el-tabs__item) {
  color: #606266 !important;
  font-size: 14px !important;
  padding: 0 16px !important;
  height: 32px !important;
  line-height: 32px !important;
  transition: all 0.3s !important;
}

body.dark .voice-selector-tabs :deep(.el-tabs__item) {
  color: var(--text-secondary) !important;
}

.voice-selector-tabs :deep(.el-tabs__item.is-active) {
  color: #4f46e5 !important;
  font-weight: 500 !important;
}

body.dark .voice-selector-tabs :deep(.el-tabs__item.is-active) {
  color: #6366f1 !important;
}

.voice-selector-tabs :deep(.el-tabs__active-bar) {
  background-color: transparent !important;
  background-image: linear-gradient(
    90deg, transparent 0, transparent 0%,
    #4f46e5 0, #4f46e5 100%,
    transparent 0, transparent
  ) !important;
  height: 2px !important;
}

body.dark .voice-selector-tabs :deep(.el-tabs__active-bar) {
  background-image: linear-gradient(
    90deg, transparent 0, transparent 0%,
    #6366f1 0, #6366f1 100%,
    transparent 0, transparent
  ) !important;
}

.voice-selector-tabs :deep(.el-tabs__nav-wrap::after) {
  position: static !important;
  height: 1px !important;
  background-color: #e4e7ed !important;
}

body.dark .voice-selector-tabs :deep(.el-tabs__nav-wrap::after) {
  background-color: var(--border-color) !important;
}

/* 设置更具体的选择器，确保样式覆盖 */
.voice-selector-tabs :deep(.el-tabs__nav) {
  height: auto !important;
}

.voice-cards-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  width: 100%;
}

.voice-list {
  padding-top: 10px;
  overflow-y: auto;
  scrollbar-width: thin;
  flex-grow: 1;
  scrollbar-color: #4143e65d #ffffff00;
  scrollbar-width: 4px;
}

body.dark .voice-list {
  scrollbar-color: #4143e65d #00000000;
}

.voice-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 14px;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  min-width: 120px; /* 最小宽度确保内容可读性 */
  box-sizing: border-box;
  margin-bottom: 0;
  /* 根据不同的列数自动计算宽度，而不是使用flex-basis */
  flex: 0 0 auto;
}

body.dark .voice-item {
  background-color: var(--bg-secondary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.voice-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

body.dark .voice-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.voice-item.active {
  border: 2px solid #409eff;
  background-color: rgba(64, 158, 255, 0.05);
}

body.dark .voice-item.active {
  border: 2px solid var(--primary-color);
  background-color: rgba(64, 158, 255, 0.1);
}

.voice-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1;
  min-width: 0; /* 确保文本可以正确截断 */
  overflow: hidden; /* 防止内容溢出 */
}

.voice-name {
  font-weight: 500;
  color: #303133;
  font-size: 15px;
  display: flex;
  gap: 10px;
  align-items: center;
  transition: color 0.3s;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

body.dark .voice-name {
  color: var(--text-primary);
}

.voice-language {
  color: #909399;
  font-size: 13px;
  margin-top: 4px;
  transition: color 0.3s;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
}

body.dark .voice-language {
  color: var(--text-secondary);
}

/* 播放按钮样式优化 */
.voice-play {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(64, 160, 255, 0.5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
  position: relative;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
}

.voice-play:hover {
  transform: scale(1.1);
  background-color: #409eff;
  box-shadow: 0 4px 10px rgba(64, 158, 255, 0.4);
}

.play-icon {
  width: 18px;
  height: 18px;
}

/* 播放状态 */
.voice-play.playing {
  background-color: #67c23a;
  box-shadow: 0 2px 6px rgba(103, 194, 58, 0.3);
}

.voice-play.playing:hover {
  background-color: #85ce61;
  box-shadow: 0 4px 10px rgba(103, 194, 58, 0.4);
}

/* 波形动画 */
.wave-animation {
  position: absolute;
  left: 50%;
  top: -14px;
  transform: translateX(-50%);
  display: flex;
  align-items: flex-end;
  height: 12px;
  width: 14px;
  gap: 2px;
}

.wave-bar {
  width: 2px;
  background-color: #67c23a;
  border-radius: 1px;
  animation: waveAnimation 0.8s infinite ease-in-out;
}

.wave-bar:nth-child(1) {
  height: 5px;
  animation-delay: 0s;
}

.wave-bar:nth-child(2) {
  height: 8px;
  animation-delay: 0.2s;
}

.wave-bar:nth-child(3) {
  height: 6px;
  animation-delay: 0.4s;
}

.wave-bar:nth-child(4) {
  height: 7px;
  animation-delay: 0.6s;
}

@keyframes waveAnimation {
  0%, 100% {
    transform: scaleY(0.6);
  }
  50% {
    transform: scaleY(1);
  }
}

/* 加载状态 */
.voice-loading, .voice-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  background-color: #f9fafc;
  border-radius: 8px;
  margin-bottom: 0;
  flex-grow: 1;
  transition: background-color 0.3s;
}

body.dark .voice-loading, 
body.dark .voice-empty {
  background-color: var(--bg-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(64, 158, 255, 0.2);
  border-top-color: #409eff;
  border-radius: 50%;
  animation: spin 1s infinite linear;
  margin-bottom: 10px;
}

body.dark .loading-spinner {
  border: 3px solid rgba(64, 158, 255, 0.1);
  border-top-color: var(--primary-color);
}

.loading-text {
  color: #909399;
  font-size: 14px;
  transition: color 0.3s;
}

body.dark .loading-text {
  color: var(--text-secondary);
}

.voice-empty {
  color: #909399;
  transition: color 0.3s;
}

body.dark .voice-empty {
  color: var(--text-secondary);
}

.voice-empty i {
  font-size: 40px;
  margin-bottom: 10px;
  opacity: 0.5;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式布局 */
@media (min-width: 768px) and (max-width: 1024px) {
  .voice-selector-tabs :deep(.el-tabs__item) {
    padding: 0 12px !important;
    font-size: 13px !important;
  }
  
  .voice-cards-container {
    gap: 10px;
  }
  
  .voice-item {
    padding: 8px 12px;
  }
}

@media (max-width: 767px) {
  .voice-name {
    font-size: 14px;
  }
  
  .voice-language {
    font-size: 12px;
  }
  
  .voice-play {
    width: 32px;
    height: 32px;
  }
  
  .play-icon {
    width: 16px;
    height: 16px;
  }
  
  .voice-selector-tabs :deep(.el-tabs__item) {
    padding: 0 8px !important;
    font-size: 12px !important;
  }
  
  .voice-selector-tabs :deep(.el-tabs__header) {
    margin-bottom: 6px !important;
  }
  
  .custom-voice-empty {
    padding: 20px 10px;
  }
  
  .custom-voice-icon {
    font-size: 32px;
    margin-bottom: 10px;
  }
  
  .custom-voice-title {
    font-size: 15px;
    margin-bottom: 6px;
  }
  
  .custom-voice-desc {
    font-size: 13px;
    margin-bottom: 14px;
  }
  
  .voice-cards-container {
    gap: 8px;
  }
  
  .voice-item {
    padding: 8px 10px;
  }
}

/* 定制音色为空时的样式 */
.custom-voice-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 20px;
  background-color: #f9fafc00;
  min-width: 500px;
  border-radius: 8px;
  width: 100%;
  box-sizing: border-box;
  transition: background-color 0.3s;
}

body.dark .custom-voice-empty {
  background-color: var(--bg-secondary);
}

.custom-voice-icon {
  font-size: 36px;
  color: #409eff;
  margin-bottom: 12px;
  opacity: 0.8;
  transition: color 0.3s;
}

body.dark .custom-voice-icon {
  color: var(--primary-color);
}

.custom-voice-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
  transition: color 0.3s;
}

body.dark .custom-voice-title {
  color: var(--text-primary);
}

.custom-voice-desc {
  font-size: 14px;
  color: #909399;
  margin-bottom: 16px;
  transition: color 0.3s;
}

body.dark .custom-voice-desc {
  color: var(--text-secondary);
}

.custom-voice-btn {
  background-color: #409eff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s;
  margin-bottom: 10px;
}

.custom-voice-btn:hover {
  background-color: #66b1ff;
  transform: translateY(-2px);
}

.icon-plus::before {
  content: "+";
  font-size: 16px;
  font-weight: bold;
}

.custom-voice-hint {
  font-size: 12px;
  color: #909399;
  background-color: #f0f2f5;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

body.dark .custom-voice-hint {
  color: var(--text-secondary);
  background-color: var(--bg-tertiary);
}

/* 在StoryDesign的编辑模式中调整高度 */
:deep(.voice-selector-container .voice-list) {
  max-height: 250px;
}

/* 确保在编辑模式下滚动条正常工作 */
:deep(.voice-selector-container) {
  height: 100%;
  max-height: 300px;
  display: flex;
  flex-direction: column;
}

:deep(.voice-selector-container .voice-selection) {
  height: 100%;
}

/* 定制新音色卡片的特殊样式 */
.add-voice-item {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(64, 158, 255, 0.05);
  border: 2px dashed rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

body.dark .add-voice-item {
  background-color: rgba(64, 158, 255, 0.08);
  border: 2px dashed rgba(64, 158, 255, 0.2);
}

.add-voice-item:hover {
  transform: translateY(-2px);
  background-color: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.5);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
}

.add-voice-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 10px;
}

.add-voice-icon {
  font-size: 24px;
  color: #409eff;
  margin-bottom: 8px;
  opacity: 0.8;
  transition: color 0.3s;
}

body.dark .add-voice-icon {
  color: var(--primary-color);
}

.add-voice-text {
  font-size: 14px;
  color: #409eff;
  font-weight: 500;
  transition: color 0.3s;
}

body.dark .add-voice-text {
  color: var(--primary-color);
}

/* 添加音色操作区域样式 */
.voice-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 编辑按钮样式 */
.edit-name-btn {
  margin: 0;
  font-size: 14px;
  height: 24px;
  width: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  background-color: #409eff;
  border-color: #409eff;
}

.edit-name-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  background-color: #66b1ff;
  border-color: #66b1ff;
}

body.dark .edit-name-btn {
  background-color: #4f46e5;
  border-color: #4f46e5;
}

body.dark .edit-name-btn:hover {
  background-color: #6366f1;
  border-color: #6366f1;
}

/* 删除按钮样式 */
.delete-voice-btn {
  margin: 0;
  font-size: 14px;
  height: 24px;
  width: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  background-color: #f56c6c;
  border-color: #f56c6c;
}

.delete-voice-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  background-color: #f78989;
  border-color: #f78989;
}

body.dark .delete-voice-btn {
  background-color: #e53e3e;
  border-color: #e53e3e;
}

body.dark .delete-voice-btn:hover {
  background-color: #f05252;
  border-color: #f05252;
}

/* 删除警告文本样式 */
.delete-warning {
  color: #f56c6c;
  font-size: 14px;
  margin-top: 8px;
}

body.dark .delete-warning {
  color: #f05252;
}

/* 删除对话框内容样式 */
.delete-voice-content {
  text-align: center;
  padding: 10px 0;
}

/* 编辑对话框样式 */
.edit-name-content {
  padding: 10px 0;
}

/* 在StoryDesign的编辑模式中调整高度 */
</style> 