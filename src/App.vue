<script setup>
import { onMounted, ref, onBeforeUnmount, watch, provide, defineAsyncComponent } from 'vue';
// 导入Layout组件
import Layout from './components/parent/Layout.vue';
// 导入版本检测服务和组件
import versionService from './services/versionService.js';
import VersionUpdateDialog from './components/VersionUpdateDialog.vue';
// 懒加载非核心组件
const ImageViewer = defineAsyncComponent(() => import('./components/parent/ImageViewer.vue'));
const VideoViewer = defineAsyncComponent(() => import('./components/parent/VideoViewer.vue'));
const AIImageEditor = defineAsyncComponent(() => import('./components/create/AIImageEditor.vue'));
const AudioViewer = defineAsyncComponent(() => import('./components/parent/AudioViewer.vue'));

// 是否处于开发环境
const isDev = import.meta.env.DEV;
const showDevTools = ref(false);
const showOutlines = ref(false);
const showInfo = ref(false);
const windowWidth = ref(window.innerWidth);
const windowHeight = ref(window.innerHeight);

// 用户积分全局变量
const userPoints = ref(0);

// 更新用户积分的函数
const updateUserPoints = (points) => {
  userPoints.value = points;
  localStorage.setItem('userPoints', points.toString());
  // 触发积分更新事件
  window.dispatchEvent(new CustomEvent('user-points-updated', { 
    detail: { points } 
  }));
};

// 格式化数字
const formatNumber = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

// 将积分相关函数提供给子组件
provide('userPoints', userPoints);
provide('updateUserPoints', updateUserPoints);
provide('formatNumber', formatNumber);

// 图片预览状态
const previewVisible = ref(false)
const previewImageUrl = ref('')
const previewImageAlt = ref('')
const previewImageTitle = ref('')
const previewImageDescription = ref('')

// 视频预览状态
const videoPreviewVisible = ref(false)
const videoPreviewUrl = ref('')
const videoPreviewTitle = ref('')
const videoPreviewPrompt = ref('')
const videoPreviewResolution = ref('')
const videoPreviewDuration = ref(0)

// 音频预览状态
const audioPreviewVisible = ref(false)
const audioPreviewUrl = ref('')
const audioPreviewTitle = ref('')
const audioPreviewDescription = ref('')
const audioPreviewCoverUrl = ref('')
const audioPreviewArtist = ref('')
const audioPreviewAlbum = ref('')
const audioPreviewFileFormat = ref('')
const audioPreviewFileSize = ref(0)

// AI修图状态
const aiEditorVisible = ref(false);
const aiEditorImageUrl = ref('');
const currentConversationId = ref('');
const aiEditorMode = ref('');
const aiEditorPrimaryId = ref('');
const aiEditorSecondaryId = ref('');
// 用于缓存分镜的scene_id
const currentShotSceneId = ref('');

// 版本更新相关状态
const showVersionDialog = ref(false);
const currentVersionInfo = ref(null);
const latestVersionInfo = ref(null);

// 打开图片预览
const openImagePreview = (url, altOrOptions = '') => {
  previewImageUrl.value = url
  
  // 判断第二个参数是字符串还是对象
  if (typeof altOrOptions === 'string') {
    // 兼容原有调用方式
    previewImageAlt.value = altOrOptions
    previewImageTitle.value = ''
    previewImageDescription.value = ''
  } else {
    // 新的调用方式，支持更多参数
    previewImageAlt.value = altOrOptions.alt || ''
    previewImageTitle.value = altOrOptions.title || ''
    previewImageDescription.value = altOrOptions.description || ''
  }
  
  previewVisible.value = true
}

// 关闭图片预览
const closeImagePreview = () => {
  previewVisible.value = false
}

// 打开视频预览
const openVideoPreview = (url, prompt = '', options = {}) => {
  videoPreviewUrl.value = url
  videoPreviewPrompt.value = prompt
  videoPreviewTitle.value = options.title || ''
  videoPreviewResolution.value = options.resolution || ''
  videoPreviewDuration.value = options.duration || 0
  videoPreviewVisible.value = true
}

// 关闭视频预览
const closeVideoPreview = () => {
  videoPreviewVisible.value = false
}

// 打开音频预览
const openAudioPreview = (url, options = {}) => {
  audioPreviewUrl.value = url
  audioPreviewTitle.value = options.title || ''
  audioPreviewDescription.value = options.description || ''
  audioPreviewCoverUrl.value = options.coverUrl || ''
  audioPreviewArtist.value = options.artist || ''
  audioPreviewAlbum.value = options.album || ''
  audioPreviewFileFormat.value = options.fileFormat || ''
  audioPreviewFileSize.value = options.fileSize || 0
  audioPreviewVisible.value = true
}

// 关闭音频预览
const closeAudioPreview = () => {
  audioPreviewVisible.value = false
}

// 打开AI修图编辑器
const openAIImageEditor = (url, conversationId = '', mode = '', itemId = '', sceneId = '') => {
  aiEditorImageUrl.value = url;
  currentConversationId.value = conversationId;
  aiEditorMode.value = mode;
  
  // 根据不同的mode设置primaryId和secondaryId
  let primaryId = '';
  let secondaryId = '';
  
  if (mode === 'character') {
    // 角色模式: primaryId = charID, secondaryId = ''
    primaryId = itemId;
    secondaryId = '';
  } else if (mode === 'scene') {
    // 场景模式: primaryId = ID, secondaryId = ''
    primaryId = itemId;
    secondaryId = '';
  } else if (mode === 'shot') {
    // 分镜模式: primaryId = scene_id, secondaryId = id
    secondaryId = itemId; // shot id
    
    // 如果传入了sceneId，优先使用传入的
    primaryId = sceneId || currentShotSceneId.value;
    
    // 保存当前sceneId，供下次使用
    if (sceneId) {
      currentShotSceneId.value = sceneId;
    }
  }
  
  // 设置到aiEditor组件的props
  aiEditorPrimaryId.value = primaryId;
  aiEditorSecondaryId.value = secondaryId;
  
  aiEditorVisible.value = true;
}

// 关闭AI修图编辑器
const closeAIEditor = () => {
  aiEditorVisible.value = false;
}

// 处理图片更新
const handleImageUpdate = (newImageUrl) => {
  // 发出全局事件，通知相应组件图片已更新
  window.dispatchEvent(new CustomEvent('ai-image-updated', {
    detail: {
      originalUrl: aiEditorImageUrl.value,
      newUrl: newImageUrl
    }
  }));
}

// 版本更新相关函数
const handleVersionUpdate = (data) => {
  console.log('[App] 检测到版本更新:', data);
  currentVersionInfo.value = data.currentVersion;
  latestVersionInfo.value = data.latestVersion;
  showVersionDialog.value = true;
};

const handleVersionRefresh = () => {
  console.log('[App] 用户选择刷新页面');
  // 这里可以添加一些清理逻辑
  versionService.forceRefresh();
};

const handleVersionLater = () => {
  console.log('[App] 用户选择稍后提醒');
  showVersionDialog.value = false;
  // 可以设置一个较短的延迟后再次检查
  setTimeout(() => {
    versionService.checkVersion();
  }, 10 * 60 * 1000); // 10分钟后再次检查
};

const handleVersionClose = () => {
  console.log('[App] 用户关闭版本提醒');
  showVersionDialog.value = false;
};

// 更新窗口尺寸
const updateWindowDimensions = () => {
  windowWidth.value = window.innerWidth;
  windowHeight.value = window.innerHeight;
};

// 监听showOutlines变化
watch(showOutlines, (newVal) => {
  if (newVal) {
    document.body.classList.add('show-outlines');
  } else {
    document.body.classList.remove('show-outlines');
  }
});

// 在组件挂载后添加开发工具和设置全局图片预览函数
onMounted(() => {
  if (isDev) {
    // 延迟加载开发工具，减少启动时的负担
    setTimeout(() => {
      showDevTools.value = true;
    }, 2000); // 延长到2秒，让主界面先加载完成
  }
  
  // 添加窗口大小变化监听
  window.addEventListener('resize', updateWindowDimensions);
  
  // 设置全局图片预览函数
  window.openImagePreview = openImagePreview;
  
  // 设置全局视频预览函数
  window.openVideoPreview = openVideoPreview;
  
  // 设置全局音频预览函数
  window.openAudioPreview = openAudioPreview;
  
  // 设置全局AI修图函数
  window.openAIImageEditor = openAIImageEditor;
  
  // 设置全局积分更新函数
  window.updateUserPoints = updateUserPoints;
  
  // 延迟非关键操作，让核心UI先渲染完成
  setTimeout(() => {
    // 加载用户积分
    const storedPoints = localStorage.getItem('userPoints');
    if (storedPoints) {
      userPoints.value = parseInt(storedPoints);
    } else {
      // 默认积分
      userPoints.value = 1250;
      localStorage.setItem('userPoints', userPoints.value.toString());
    }
    
    // 应用保存的主题设置
    const savedThemeMode = localStorage.getItem('themeMode');
    const savedDarkMode = localStorage.getItem('darkMode');
    
    // 如果有新的主题模式设置
    if (savedThemeMode) {
      if (savedThemeMode === 'dark') {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark');
      } else if (savedThemeMode === 'light') {
        document.documentElement.classList.remove('dark');
        document.body.classList.remove('dark');
      } else if (savedThemeMode === 'auto') {
        // 自动模式，检查系统首选主题
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        if (prefersDark) {
          document.documentElement.classList.add('dark');
          document.body.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
          document.body.classList.remove('dark');
        }
      }
    } 
    // 向下兼容旧版本的darkMode设置
    else if (savedDarkMode) {
      if (savedDarkMode === 'true') {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark');
        // 迁移到新的设置格式
        localStorage.setItem('themeMode', 'dark');
      } else {
        document.documentElement.classList.remove('dark');
        document.body.classList.remove('dark');
        // 迁移到新的设置格式
        localStorage.setItem('themeMode', 'light');
      }
    }
  }, 100); // 延迟100ms执行非关键逻辑
  
  // 监听分镜场景ID变更事件，用于后续AI修图
  window.addEventListener('shot-scene-id-changed', (event) => {
    if (event.detail && event.detail.sceneId) {
      currentShotSceneId.value = event.detail.sceneId;
    }
  });

  // 设置版本更新监听
  const unsubscribeVersionUpdate = versionService.onVersionUpdate(handleVersionUpdate);

  // 保存取消订阅函数，用于组件卸载时清理
  window._unsubscribeVersionUpdate = unsubscribeVersionUpdate;
});

// 组件卸载前移除事件监听和全局函数
onBeforeUnmount(() => {
  window.removeEventListener('resize', updateWindowDimensions);

  // 移除全局预览函数
  window.openImagePreview = null;
  window.openVideoPreview = null;
  window.openAudioPreview = null;
  window.openAIImageEditor = null;
  window.updateUserPoints = null;

  // 移除分镜场景ID变更事件监听
  window.removeEventListener('shot-scene-id-changed', null);

  // 取消版本更新监听
  if (window._unsubscribeVersionUpdate) {
    window._unsubscribeVersionUpdate();
    window._unsubscribeVersionUpdate = null;
  }

  // 销毁版本检测服务
  versionService.destroy();
});

window.onload = function () {
  // 禁用双指放大
  document.addEventListener('touchstart', function (event) {
    if (event.touches.length > 1) {
      event.preventDefault()
    }
  })
  // 禁用双击放大
  let lastTouchEnd = 0
  document.addEventListener(
    'touchend',
    function (event) {
      const now = new Date().getTime()
      if (now - lastTouchEnd <= 300) {
        event.preventDefault()
      }
      lastTouchEnd = now
    },
    false
  )
  document.addEventListener('gesturestart', function (event) {
    event.preventDefault()
  })
}

</script>

<template>
  <Layout>
    <router-view />
  </Layout>
  
  <!-- 全局图片预览组件 -->
  <ImageViewer 
    v-if="previewVisible" 
    :visible="previewVisible" 
    :image-url="previewImageUrl" 
    :alt="previewImageAlt" 
    :title="previewImageTitle"
    :description="previewImageDescription"
    @close="closeImagePreview" 
  />
  
  <!-- 全局视频预览组件 -->
  <VideoViewer 
    v-if="videoPreviewVisible" 
    :visible="videoPreviewVisible" 
    :video-url="videoPreviewUrl" 
    :title="videoPreviewTitle" 
    :prompt="videoPreviewPrompt"
    :resolution="videoPreviewResolution"
    :duration="videoPreviewDuration"
    @close="closeVideoPreview" 
  />
  
  <!-- 全局音频预览组件 -->
  <AudioViewer
    v-if="audioPreviewVisible"
    :visible="audioPreviewVisible"
    :audio-url="audioPreviewUrl"
    :title="audioPreviewTitle"
    :description="audioPreviewDescription"
    :cover-url="audioPreviewCoverUrl"
    :artist="audioPreviewArtist"
    :album="audioPreviewAlbum"
    :file-format="audioPreviewFileFormat"
    :file-size="audioPreviewFileSize"
    @close="closeAudioPreview"
  />
  
  <!-- 全局AI修图组件 -->
  <AIImageEditor
    :visible="aiEditorVisible"
    :image-url="aiEditorImageUrl"
    :conversation-id="currentConversationId"
    :editor-mode="aiEditorMode"
    :primary-id="aiEditorPrimaryId"
    :secondary-id="aiEditorSecondaryId"
    @close="closeAIEditor"
    @update="handleImageUpdate"
  />

  <!-- 版本更新提醒对话框 -->
  <VersionUpdateDialog
    :visible="showVersionDialog"
    :current-version="currentVersionInfo"
    :latest-version="latestVersionInfo"
    :show-later-button="true"
    :show-close-button="true"
    :prevent-close="false"
    @refresh="handleVersionRefresh"
    @later="handleVersionLater"
    @close="handleVersionClose"
  />
</template>

<style>
body {
  margin: 0;
  padding: 0;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f7fa;
  transition: background-color 0.3s, color 0.3s;
}

body.dark {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

#app {
  height: 100vh;
  width: 100vw;
  /* display: flex; */
  flex-direction: column;
  transition: background-color 0.3s;
}

body.dark #app {
  background-color: var(--bg-primary);
}

.container {
  width: 100%;
  /* max-width: 1200px; */
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
  transition: color 0.3s;
}

body.dark .page-title {
  color: var(--text-primary);
}

.card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
  transition: background-color 0.3s, box-shadow 0.3s;
}

body.dark .card {
  background-color: var(--bg-card);
  box-shadow: 0 2px 12px 0 var(--shadow-color);
}

/* DevTools样式 */
.dev-tools {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
}

.dev-panel {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 8px;
  padding: 12px;
  width: 200px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.dev-panel h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 5px;
}

.dev-panel ul {
  list-style: none;
  padding: 0;
  margin: 0 0 10px 0;
}

.dev-panel li {
  margin-bottom: 5px;
}

.dev-info {
  font-size: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 8px;
}

.dev-info p {
  margin: 5px 0;
}

/* 显示布局边界辅助器 */
body.show-outlines * {
  outline: 1px solid rgba(255, 0, 0, 0.2);
}

body.show-outlines div[class*="section"]::before {
  background-color: rgba(0, 0, 255, 0.1) !important;
}

body.show-outlines .container {
  outline: 1px solid rgba(0, 255, 0, 0.5);
}
</style>
